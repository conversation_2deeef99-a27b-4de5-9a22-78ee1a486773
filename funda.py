import requests
import numpy as np
import pandas as pd

from io import BytesIO
import os

import time
from tqdm import tqdm
import json
from PIL import Image
from bs4 import BeautifulSoup as bs

# python /home/<USER>/projects/indoorEnergy/funda.py

headers = {
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36',
}

# set proxy
proxies = { 'http' : 'https://smaoran:nwkI4r4S2K@***************:50100' } 

def save_image(imgUrl, imgPath):
    # set image save fold
    try:
        # print(imgUrl)
        response = requests.get(imgUrl, headers=headers, proxies=proxies, timeout=20)
        img = Image.open(BytesIO(response.content))
        img.save(imgPath)
        return 1
    except:
        print('{} download failed!'.format(imgPath))
        return 0
        
# def get_price(soup):
#     try:
#         return soup.find("span", class_='total').text.strip() + soup.find("span", class_='unit').text.strip()
#     except:
#         return None
    
# def get_area(soup):
#     try:
#         return soup.find('div', class_='area').find('div', class_='mainInfo').text.strip()
#     except:
#         return None
    
# def get_imgURL(soup):
#     imgContainer = soup.find('ul', class_='smallpic')
#     imgURLs = []
#     try:
#         for img in imgContainer.find_all('li'):
#             imgURLs.append(img.get('data-src'))
#     except:
#         pass
#     return imgURLs  

# def get_id(url):
#     return url.split('/')[-1].split('.')[0]
        
def get_url_from_page(pageURL):
    # get search page content
    response = requests.get(pageURL, headers=headers, proxies=proxies, timeout=20)
    # convert page content to beautiful soup
    searchPage = bs(response.content, "html.parser")
    # get all listings in current page
    listings = searchPage.find_all('div', {'data-test-id':'search-result-item'})
    # 
    listingsURLs = []
    for listing in listings:
        try:
            listingsURLs.append(listing.find('a', {'data-test-id':"object-image-link"}, href=True)['href'])
        except:
            pass
        
    return listingsURLs

def getLinkItemList(s):
    return s.find_all('img', {'class':'w-full object-cover lg:h-full'})

def getImageLink(linkItemList):
    linkList = []
    for linkItem in linkItemList:
        try:
            linkList.append(linkItem['srcset'].split(',\n')[-1].strip().split(' ')[0])
        except:
            pass
    return linkList

def formatLinkImage(url):
    return url + 'overzicht'

def getInfoList(s):
    infoList = []
    for objectList in s.find_all('section', {'class':'mt-6 md:mt-7'}):
        for info in objectList.find_all('div'):
            if info.get_text(separator='; ').strip() != '':
                infoList.append(info.get_text(separator='; ').strip())
    return infoList
    
def main():
    # setup download folder
    cityImgFolder = "/media/data_16T/maoransu/projects/indoorEnergy/data/funda/amsterdam/img"
    if not os.path.exists(cityImgFolder):
        os.makedirs(cityImgFolder)
        
    # iterate through search pages
    
    pageNo = 0
    allImgCount = 0
    
    allProperties = []
    propertyInfomation = []
    
    while pageNo < 65:
        print("Current downloading page: {} | Images downloaded: {}".format(pageNo, allImgCount))
        requestUrl = "https://www.funda.nl/zoeken/huur?selected_area=%5B%22amsterdam%22%5D&search_result={}".format(pageNo)
                    #   https://www.funda.nl/zoeken/huur?selected_area=%5B%22amsterdam%22%5D&search_result=2
        print(requestUrl)

        
        listingURLs = get_url_from_page(requestUrl)
        allProperties.extend(listingURLs)

        print(listingURLs)

        with open("/media/data_16T/maoransu/projects/indoorEnergy/data/funda/amsterdam/amsterdam_0913.json", 'w') as f:
            json.dump(allProperties, f)
        
        # scrape over all the listings in the current page
        for listingUrl in listingURLs:
            # download images
            # make link: add to the end of the link: #overzicht
            urlForImage = formatLinkImage(listingUrl)
            response = requests.get(urlForImage, headers=headers, proxies=proxies, )
            soup = bs(response.content, "html.parser")
            # get item link list
            linkItemList = getLinkItemList(soup)
            # get image urls
            imgURLs = getImageLink(linkItemList)
            # setup property image folder
            propertyImgFolder = os.path.join(cityImgFolder, listingUrl[:-1].split('/')[-1])
            if not os.path.exists(propertyImgFolder):
                os.makedirs(propertyImgFolder)
                for imgURL in imgURLs:
                    # get image name
                    imgName = imgURL.split('/')[-1]
                    # save image
                    imgPath = os.path.join(propertyImgFolder, imgName)
                    allImgCount += save_image(imgURL, imgPath)
                    time.sleep(2)

                # download information
                response = requests.get(listingUrl, headers=headers, proxies=proxies)
                soup = bs(response.content, "html.parser")
                # get information list
                infoList = getInfoList(soup)
                propertyInfomation.append({'imgFolder': propertyImgFolder, 'infoList': infoList, 'propertyURL': listingUrl})
                propertyInfomationDF = pd.DataFrame(propertyInfomation)
                propertyInfomationDF.to_csv('/media/data_16T/maoransu/projects/indoorEnergy/data/funda/amsterdam/amsterdam_0913.csv', index=False)
                time.sleep(100)
            
        time.sleep(100)

        pageNo += 1
    
#     with open("/media/data_16T/maoransu/projects/indoorEnergy/data/funda/amsterdam/amsterdam_1018.json", 'w') as f:
#             json.dump(allProperties, f)  
#         for propertyURL in listingURLs:
# #             print("Current downloading property:{}".format(propertyURL))
#             try:
#                 propertyID, price, area, propertyImgFolder, imgCount = get_property_from_page(propertyURL)
#                 allProperties.append(
#                     {
#                         "propertyID": propertyID,
#                         "price": price,
#                         "area": area,
#                         "propertyImgFolder": propertyImgFolder
#                     })

#                 allImgCount += imgCount

#                 with open("/media/data_16T/nwl/datasets/beike/shanghai/shanghai_0120.json", 'w') as f:
#                     json.dump(allProperties, f)

#                 time.sleep(30)
#             except:
#                 pass      

if __name__ == '__main__':
    main()
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    