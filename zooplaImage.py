from ast import main
import os
import requests
import time
from itertools import cycle

# Headers to mimic a real browser
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Your 10 proxies - replace with your actual proxy list
proxies = [
    'http://proxy1:port1',
    'http://proxy2:port2',
    'http://proxy3:port3',
    'http://proxy4:port4',
    'http://proxy5:port5',
    'http://proxy6:port6',
    'http://proxy7:port7',
    'http://proxy8:port8',
    'http://proxy9:port9',
    'http://proxy10:port10',
]


# Create a cycle to rotate through proxies
proxy_cycle = cycle(proxies)

base_dir = 'image/img'

for property_id, urls in property_images.items():
    # Create a directory for each property_id
    property_dir = os.path.join(base_dir, str(property_id))
    os.makedirs(property_dir, exist_ok=True)
    
    for idx, url in enumerate(urls):
        # Get next proxy from the cycle
        current_proxy = next(proxy_cycle)
        proxy_dict = {'http': current_proxy, 'https': current_proxy}
        
        try:
            response = requests.get(
                url, 
                headers=headers, 
                proxies=proxy_dict, 
                timeout=30
            )
            response.raise_for_status()
            
            # Save image with a unique name
            ext = url.split('.')[-1].split('?')[0]  # crude way to get extension
            filename = f'image_{idx}.{ext}'
            filepath = os.path.join(property_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"Downloaded: {filename} for property {property_id} using proxy {current_proxy}")
            
            # Add a small delay to avoid overwhelming the server
            time.sleep(0.5)
            
        except Exception as e:
            print(f"Failed to download {url} for property {property_id} using proxy {current_proxy}: {e}")
            # Continue with next image even if one fails
            continue 